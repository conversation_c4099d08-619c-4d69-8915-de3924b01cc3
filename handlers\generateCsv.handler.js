const fs = require('fs');
const path = require('path');
const csv = require('fast-csv');
const logger = require('../config/logger');
const { uploadCSVFile } = require('../services/csv.service');
const { loadMappingConfig } = require('../utils/agentHelper');

/**
 * Extracts unique model names from mapping keys
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Array<string>} Array of unique model names
 */
function extractModelNames(mappingConfig) {
    const modelNames = new Set();

    Object.keys(mappingConfig).forEach(key => {
        if (key.includes('.')) {
            const modelName = key.split('.')[0];
            modelNames.add(modelName);
        }
    });

    return Array.from(modelNames);
}

/**
 * Determines if mapping values are column names (strings) or numerical positions (numbers)
 * @param {Object} mappingConfig - Mapping configuration object
 * @returns {Object} Object with type ('column' or 'position') and processed mapping
 */
function analyzeMappingType(mappingConfig) {
    const values = Object.values(mappingConfig);

    // Check if all values are numbers (as strings or actual numbers)
    const allNumbers = values.every(value => {
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
    });

    if (allNumbers) {
        // Convert string numbers to actual numbers and sort by position
        const sortedEntries = Object.entries(mappingConfig)
            .map(([key, value]) => [key, Number(value)])
            .sort((a, b) => a[1] - b[1]);

        return {
            type: 'position',
            mapping: Object.fromEntries(sortedEntries)
        };
    } else {
        return {
            type: 'column',
            mapping: mappingConfig
        };
    }
}

/**
 * Generic function to generate CSV data from any model using mapping configuration
 * @param {Object} params - Handler parameters
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Result object with file info and metrics
 */
async function generateCsv({ agent, performanceMonitor }) {
    try {
        logger.info(`[CSV Handler] Starting CSV generation for agent: ${agent.name}`);
        
        // Load mapping configuration
        const mappingConfig = loadMappingConfig(agent.mapping);
        logger.info(`[CSV Handler] Loaded mapping configuration for: ${agent.mapping}`);

        // Analyze mapping type (column names vs positions)
        const { type: mappingType, mapping: processedMapping } = analyzeMappingType(mappingConfig);
        logger.info(`[CSV Handler] Mapping type detected: ${mappingType}`);

        // Extract unique model names from mapping
        const modelNames = extractModelNames(mappingConfig);
        logger.info(`[CSV Handler] Models required: ${modelNames.join(', ')}`);

        // Dynamically require models
        const models = {};
        const allModels = require('../models');

        modelNames.forEach(modelName => {
            if (allModels[modelName]) {
                models[modelName] = allModels[modelName];
            } else {
                throw new Error(`Model ${modelName} not found in models`);
            }
        });

        // For now, we'll use the first model as primary (can be enhanced for joins later)
        const primaryModelName = modelNames[0];
        const primaryModel = models[primaryModelName];

        // Get all records from primary model
        performanceMonitor?.startStep(`Fetch ${primaryModelName} Data`, { agentName: agent.name });
        const records = await primaryModel.findAll({
            raw: true,
            limit: agent.batch_size || 1000 // Limit records based on batch size
        });
        performanceMonitor?.endStep(`Fetch ${primaryModelName} Data`, { recordCount: records.length });

        if (records.length === 0) {
            logger.warn(`[CSV Handler] No ${primaryModelName} records found for CSV generation`);
            return null;
        }

        // Prepare CSV data based on mapping type
        performanceMonitor?.startStep('Transform Data', { recordCount: records.length });
        const csvData = [];

        if (mappingType === 'column') {
            // Column name mapping: use values as headers
            const headers = Object.values(processedMapping);
            csvData.push(headers);

            // Transform each record
            records.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, csvColumn]) => {
                    // Extract field name from "Model.field_name" format
                    const fieldName = modelField.includes('.') ? modelField.split('.')[1] : modelField;
                    const value = record[fieldName] || '';
                    csvRow.push(value);
                });
                csvData.push(csvRow);
            });
        } else {
            // Position mapping: no headers, order by position
            records.forEach(record => {
                const csvRow = [];
                Object.entries(processedMapping).forEach(([modelField, position]) => {
                    // Extract field name from "Model.field_name" format
                    const fieldName = modelField.includes('.') ? modelField.split('.')[1] : modelField;
                    const value = record[fieldName] || '';
                    csvRow[position] = value; // Place value at specific position
                });
                // Fill any gaps with empty strings
                for (let i = 0; i < csvRow.length; i++) {
                    if (csvRow[i] === undefined) csvRow[i] = '';
                }
                csvData.push(csvRow);
            });
        }

        performanceMonitor?.endStep('Transform Data', { transformedRows: csvData.length - (mappingType === 'column' ? 1 : 0) });

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${agent.mapping}_export_${timestamp}.csv`;

        // Create temporary file for processing
        const tempDir = './downloads/temp';
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        const tempFilePath = path.join(tempDir, filename);

        // Write CSV file to temporary location
        performanceMonitor?.startStep('Generate CSV File', { tempFilePath, rowCount: csvData.length });
        await new Promise((resolve, reject) => {
            const writeStream = fs.createWriteStream(tempFilePath);
            csv.write(csvData, { headers: false })
                .pipe(writeStream)
                .on('finish', resolve)
                .on('error', reject);
        });
        performanceMonitor?.endStep('Generate CSV File', { fileSize: fs.statSync(tempFilePath).size });

        const dataRows = mappingType === 'column' ? csvData.length - 1 : csvData.length;
        logger.info(`[CSV Handler] CSV file generated successfully: ${tempFilePath}`);
        logger.info(`[CSV Handler] Generated ${dataRows} data rows from ${primaryModelName} model`);

        // Upload CSV file to configured destination
        performanceMonitor?.startStep('Upload CSV to Destination', {
            destination: agent.source,
            agentName: agent.name,
            fileName: filename
        });
        console.log("reached till here\n\n\n********")
        console.log(uploadCSVFile)
        const uploadResult = await uploadCSVFile(tempFilePath, agent, performanceMonitor);

        performanceMonitor?.endStep('Upload CSV to Destination', {
            success: uploadResult.success,
            destination: uploadResult.destination,
            uploadedPath: uploadResult.uploadedPath,
            error: uploadResult.error
        });

        // Clean up temporary file
        try {
            fs.unlinkSync(tempFilePath);
            logger.info(`[CSV Handler] Cleaned up temporary file: ${tempFilePath}`);
        } catch (error) {
            logger.warn(`[CSV Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`);
        }

        if (!uploadResult.success) {
            throw new Error(`Failed to upload CSV to ${agent.source}: ${uploadResult.error}`);
        }

        return {
            tempFilePath,
            uploadResult,
            dataRows,
            primaryModel: primaryModelName,
            totalRecords: records.length,
            fileName: filename
        };

    } catch (error) {
        logger.error('[CSV Handler] Error generating CSV from model data:', error.message);
        throw error;
    }
}

module.exports = generateCsv;
