const logger = require('../config/logger');
const path = require("path");

/**
 * Get RabbitMQ channel from global context or establish new connection
 * @returns {Promise<Object>} RabbitMQ channel
 */
async function getRabbitMQChannel() {
  try {
    // Use global channel if available
    if (global.channel) {
      return global.channel;
    }

    // Establish new connection if global channel not available
    const connectRabbitmq = require('../config/rabbitmq');
    const { channel } = await connectRabbitmq();

    if (!channel) {
      throw new Error('Failed to establish RabbitMQ connection');
    }

    global.channel = channel;
    return channel;

  } catch (error) {
    logger.error('[Agent Helper] Failed to get RabbitMQ channel:', error);
    throw error;
  }
}

/**
 * Setup queue consumer with error handling
 * @param {string} queueName - Name of the queue
 * @param {Function} messageHandler - Function to handle messages
 * @param {Object} options - Consumer options
 * @returns {Promise<void>}
 */
async function setupQueueConsumer(queueName, messageHandler, options = {}) {
  try {
    const channel = await getRabbitMQChannel();

    // Assert queue exists
    await channel.assertQueue(queueName, { durable: true });

    logger.info(`[Agent Helper] Setting up consumer for queue: ${queueName}`);

    // Set up consumer
    await channel.consume(queueName, async (msg) => {
      if (!msg) return;

      try {
        await messageHandler(msg, channel);
      } catch (error) {
        logger.error(`[Agent Helper] Error processing message from queue ${queueName}:`, error);

        // Reject message and requeue (you might want to implement dead letter queue)
        channel.nack(msg, false, true);
      }
    }, {
      noAck: false, // Ensure manual acknowledgment
      ...options
    });

    logger.info(`[Agent Helper] Consumer established for queue: ${queueName}`);

  } catch (error) {
    logger.error(`[Agent Helper] Failed to setup consumer for queue ${queueName}:`, error);
    throw error;
  }
}

/**
 * Send message to RabbitMQ queue
 * @param {string} queueName - Target queue name
 * @param {Object} message - Message to send
 * @param {Object} options - Send options
 * @returns {Promise<boolean>} Success status
 */
async function sendToQueue(queueName, message, options = {}) {
  try {
    const channel = await getRabbitMQChannel();

    // Assert queue exists
    await channel.assertQueue(queueName, { durable: true });

    // Send message
    const messageBuffer = Buffer.from(JSON.stringify(message));
    const result = channel.sendToQueue(queueName, messageBuffer, {
      persistent: true,
      ...options
    });

    if (result) {
      logger.debug(`[Agent Helper] Message sent to queue ${queueName}:`, {
        messageId: options.messageId,
        messageSize: messageBuffer.length
      });
    }

    return result;

  } catch (error) {
    logger.error(`[Agent Helper] Failed to send message to queue ${queueName}:`, error);
    throw error;
  }
}

/**
 * Get agent settings as key-value object
 * @param {string} agentId - Agent ID
 * @returns {Promise<Object>} Agent settings
 */
async function getAgentSettings(agentId) {
  try {
    const { AgentSetting } = require('../models');

    const agentSettings = await AgentSetting.findAll({
      where: { agent_id: agentId }
    });

    const settings = {};
    agentSettings.forEach(setting => {
      settings[setting.key] = setting.value;
    });

    return settings;

  } catch (error) {
    logger.error(`[Agent Helper] Failed to get agent settings for ${agentId}:`, error);
    throw error;
  }
}

/**
 * Load mapping configuration for agent
 * @param {string} mappingName - Name of the mapping file
 * @returns {Object} Mapping configuration
 */
function loadMappingConfig(mappingName) {
  try {
    const mappingConfig = require(path.join(process.cwd(), `mappings/${mappingName}.mapping.json`));
    return mappingConfig;
  } catch (error) {
    logger.error(`[Agent Helper] Failed to load mapping config ${mappingName}:`, error);
    throw new Error(`Mapping configuration '${mappingName}' not found`);
  }
}

/**
 * Get handler function dynamically
 * @param {string} handlerName - Name of the handler
 * @returns {Function} Handler function
 */
function getHandler(handlerName) {
  try {
    const handlers = require(path.join(process.cwd(), 'handlers'));
    const handler = handlers[handlerName];

    if (!handler || typeof handler !== 'function') {
      throw new Error(`Handler '${handlerName}' not found`);
    }

    return handler;
  } catch (error) {
    logger.error(`[Agent Helper] Failed to get handler ${handlerName}:`, error);
    throw error;
  }
}

/**
 * Setup graceful shutdown handlers
 * @param {Function} shutdownCallback - Callback to execute on shutdown
 */
function setupGracefulShutdown(shutdownCallback) {
  const handleShutdown = async (signal) => {
    logger.info(`[Agent Helper] Received ${signal}, shutting down gracefully...`);

    try {
      if (shutdownCallback) {
        await shutdownCallback();
      }

      // Close RabbitMQ connection
      if (global.channel) {
        await global.channel.close();
      }

      logger.info('[Agent Helper] Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      logger.error('[Agent Helper] Error during shutdown:', error);
      process.exit(1);
    }
  };

  process.on('SIGINT', () => handleShutdown('SIGINT'));
  process.on('SIGTERM', () => handleShutdown('SIGTERM'));
}

/**
 * Validate agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {boolean} Validation result
 */
function validateAgentConfig(agent) {
  const requiredFields = ['name', 'type', 'source', 'queue', 'handler'];

  for (const field of requiredFields) {
    if (!agent[field]) {
      logger.error(`[Agent Helper] Agent validation failed: missing ${field}`);
      return false;
    }
  }

  if (!['Inbound', 'Outbound'].includes(agent.type)) {
    logger.error(`[Agent Helper] Agent validation failed: invalid type ${agent.type}`);
    return false;
  }

  return true;
}

/**
 * Log agent activity with consistent format
 * @param {string} agentName - Agent name
 * @param {string} activity - Activity description
 * @param {Object} metadata - Additional metadata
 */
function logAgentActivity(agentName, activity, metadata = {}) {
  logger.info(`[Agent: ${agentName}] ${activity}`, {
    agentName,
    timestamp: new Date().toISOString(),
    ...metadata
  });
}

module.exports = {
  getRabbitMQChannel,
  setupQueueConsumer,
  sendToQueue,
  getAgentSettings,
  loadMappingConfig,
  getHandler,
  setupGracefulShutdown,
  validateAgentConfig,
  logAgentActivity
};
